import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { CreateUserSchema } from '@/lib/validations'
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  handleDatabaseError,
  logActivity,
  buildSearchFilter,
  buildSortOrder,
  getPaginationParams
} from '@/lib/api-utils'

// GET /api/users - Get all users with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    const search = searchParams.get('search') || undefined
    const department = searchParams.get('department') || undefined
    const isActive = searchParams.get('isActive')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'

    // Build filters
    const where: any = {
      ...buildSearchFilter(search, ['name', 'email', 'department']),
    }

    if (department) {
      where.department = department
    }

    if (isActive !== null && isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    // Get users with pagination
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        orderBy: buildSortOrder(sortBy, sortOrder),
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              borrowingTransactions: {
                where: { status: 'ACTIVE' }
              },
              consumptionTransactions: true,
            },
          },
        },
      }),
      prisma.user.count({ where }),
    ])

    const totalPages = Math.ceil(total / limit)

    return successResponse(users, undefined, {
      page,
      limit,
      total,
      totalPages,
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return handleDatabaseError(error)
  }
}

// POST /api/users - Create new user
export async function POST(request: NextRequest) {
  try {
    const validation = await validateRequest(request, CreateUserSchema)
    if (!validation.success) {
      return validation.response
    }

    const { name, email, phone, department, isActive } = validation.data

    // Check if user with same email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      return errorResponse('User with this email already exists', 409)
    }

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        phone,
        department,
        isActive,
      },
      include: {
        _count: {
          select: {
            borrowingTransactions: {
              where: { status: 'ACTIVE' }
            },
            consumptionTransactions: true,
          },
        },
      },
    })

    // Log activity
    await logActivity('USER', user.id, 'CREATE', undefined, undefined, user)

    return successResponse(user, 'User created successfully')
  } catch (error) {
    console.error('Error creating user:', error)
    return handleDatabaseError(error)
  }
}
