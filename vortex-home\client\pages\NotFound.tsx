import { Link, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Home, AlertTriangle } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname,
    );
  }, [location.pathname]);

  return (
    <div className="flex items-center justify-center h-full">
      <div className="glass rounded-xl p-12 text-center max-w-md">
        <AlertTriangle className="w-16 h-16 text-primary mx-auto mb-4" />
        <h1 className="text-3xl font-bold text-foreground mb-2">404 - Page Not Found</h1>
        <p className="text-muted-foreground mb-6">
          The page you're looking for doesn't exist or has been moved.
        </p>
        <Button asChild className="hover-lift">
          <Link to="/">
            <Home className="w-4 h-4 mr-2" />
            Go back to Dashboard
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
