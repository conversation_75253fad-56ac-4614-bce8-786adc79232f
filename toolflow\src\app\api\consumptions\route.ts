import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { CreateConsumptionSchema } from '@/lib/validations'
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  handleDatabaseError,
  logActivity,
  buildSearchFilter,
  buildSortOrder,
  getPaginationParams,
  checkMaterialStock
} from '@/lib/api-utils'

// GET /api/consumptions - Get all consumption transactions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    const search = searchParams.get('search') || undefined
    const userId = searchParams.get('userId') || undefined
    const projectName = searchParams.get('projectName') || undefined
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'

    // Build filters
    const where: any = {
      ...buildSearchFilter(search, ['purpose', 'projectName']),
    }

    if (userId) {
      where.userId = userId
    }

    if (projectName) {
      where.projectName = {
        contains: projectName,
        mode: 'insensitive'
      }
    }

    // Get consumptions with pagination
    const [consumptions, total] = await Promise.all([
      prisma.consumptionTransaction.findMany({
        where,
        orderBy: buildSortOrder(sortBy, sortOrder),
        skip,
        take: limit,
        include: {
          user: {
            select: { id: true, name: true, email: true, department: true }
          },
          consumptionItems: {
            include: {
              material: {
                select: { 
                  id: true, 
                  name: true, 
                  unit: true,
                  category: { select: { name: true } }
                }
              }
            }
          }
        },
      }),
      prisma.consumptionTransaction.count({ where }),
    ])

    // Add computed fields
    const consumptionsWithTotals = consumptions.map(consumption => ({
      ...consumption,
      totalItems: consumption.consumptionItems.length,
      totalQuantity: consumption.consumptionItems.reduce((sum, item) => sum + Number(item.quantity), 0),
      calculatedTotalValue: consumption.consumptionItems.reduce(
        (sum, item) => sum + (Number(item.totalValue) || 0), 0
      )
    }))

    const totalPages = Math.ceil(total / limit)

    return successResponse(consumptionsWithTotals, undefined, {
      page,
      limit,
      total,
      totalPages,
    })
  } catch (error) {
    console.error('Error fetching consumptions:', error)
    return handleDatabaseError(error)
  }
}

// POST /api/consumptions - Create new consumption transaction
export async function POST(request: NextRequest) {
  try {
    const validation = await validateRequest(request, CreateConsumptionSchema)
    if (!validation.success) {
      return validation.response
    }

    const { userId, purpose, projectName, notes, items } = validation.data

    // Verify user exists and is active
    const user = await prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      return errorResponse('User not found', 404)
    }

    if (!user.isActive) {
      return errorResponse('User is not active', 400)
    }

    // Check material stock for all items
    const materialChecks = await Promise.all(
      items.map(async (item) => {
        try {
          const material = await checkMaterialStock(item.materialId, item.quantity)
          return { ...item, material }
        } catch (error) {
          throw new Error(`${error}`)
        }
      })
    )

    // Calculate total value
    const totalValue = items.reduce((sum, item) => {
      const itemTotal = item.unitPrice ? item.quantity * item.unitPrice : 0
      return sum + itemTotal
    }, 0)

    // Create consumption transaction
    const consumption = await prisma.$transaction(async (tx) => {
      // Create consumption transaction
      const newConsumption = await tx.consumptionTransaction.create({
        data: {
          userId,
          purpose,
          projectName,
          notes,
          totalValue: totalValue > 0 ? totalValue : null,
        },
      })

      // Create consumption items and update material stock
      const consumptionItems = await Promise.all(
        materialChecks.map(async (item) => {
          const itemTotalValue = item.unitPrice ? item.quantity * item.unitPrice : null

          // Create consumption item
          const consumptionItem = await tx.consumptionItem.create({
            data: {
              consumptionTransactionId: newConsumption.id,
              materialId: item.materialId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalValue: itemTotalValue,
              notes: item.notes,
            },
          })

          // Update material stock
          await tx.material.update({
            where: { id: item.materialId },
            data: {
              currentQuantity: {
                decrement: item.quantity
              }
            },
          })

          return consumptionItem
        })
      )

      return { ...newConsumption, consumptionItems }
    })

    // Log activity
    await logActivity(
      'CONSUMPTION_TRANSACTION', 
      consumption.id, 
      'CONSUME', 
      userId, 
      undefined, 
      consumption,
      { 
        itemCount: items.length, 
        totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0),
        totalValue: totalValue
      }
    )

    // Fetch complete consumption data for response
    const completeConsumption = await prisma.consumptionTransaction.findUnique({
      where: { id: consumption.id },
      include: {
        user: {
          select: { id: true, name: true, email: true, department: true }
        },
        consumptionItems: {
          include: {
            material: {
              select: { 
                id: true, 
                name: true, 
                unit: true,
                category: { select: { name: true } }
              }
            }
          }
        }
      },
    })

    return successResponse(completeConsumption, 'Consumption transaction created successfully')
  } catch (error) {
    console.error('Error creating consumption:', error)
    return handleDatabaseError(error)
  }
}
