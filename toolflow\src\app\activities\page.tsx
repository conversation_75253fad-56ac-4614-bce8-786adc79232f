'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ContextualSidebar } from '@/components/ui/contextual-sidebar';
import { cn } from '@/lib/utils';
import {
  Activity,
  Search,
  Filter,
  Calendar,
  Package,
  User,
  AlertTriangle,
  Clock,
  CheckCircle,
  Eye,
  FileText,
  ArrowUpRight,
  RotateCcw,
  Plus,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import Link from 'next/link';

// Types
interface BorrowingTransaction {
  id: string;
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  status: string;
  purpose: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    department?: string;
  };
  borrowingItems: Array<{
    id: string;
    quantity: number;
    originalCondition: string;
    returnCondition?: string;
    returnDate?: string;
    tool: {
      id: string;
      name: string;
      condition: string;
    };
  }>;
  totalItems: number;
  isOverdue: boolean;
  daysOverdue: number;
  canExtend: boolean;
}

interface ConsumptionTransaction {
  id: string;
  consumptionDate: string;
  purpose: string;
  projectName?: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    department?: string;
  };
  consumptionItems: Array<{
    id: string;
    quantity: number;
    unitPrice?: number;
    totalValue?: number;
    material: {
      id: string;
      name: string;
      unit: string;
      category: {
        name: string;
      };
    };
  }>;
  totalItems: number;
  totalValue?: number;
}

// API functions
const fetchBorrowings = async (status?: string): Promise<BorrowingTransaction[]> => {
  try {
    const params = new URLSearchParams();
    if (status) params.append('status', status);

    const response = await fetch(`/api/borrowings?${params.toString()}`);
    const result = await response.json();

    if (result.success) {
      return result.data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching borrowings:', error);
    return [];
  }
};

const fetchConsumptions = async (): Promise<ConsumptionTransaction[]> => {
  try {
    const response = await fetch('/api/consumptions');
    const result = await response.json();

    if (result.success) {
      return result.data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching consumptions:', error);
    return [];
  }
};

// API functions will be implemented when sidebar functionality is complete

export default function Activities() {
  const [activeTab, setActiveTab] = useState("borrowing");
  const [searchQuery, setSearchQuery] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarType, setSidebarType] = useState<'borrow' | 'consume' | 'return' | 'extend'>('borrow');
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [expandedRows, setExpandedRows] = useState<string[]>([]);

  // Data states
  const [borrowings, setBorrowings] = useState<BorrowingTransaction[]>([]);
  const [consumptions, setConsumptions] = useState<ConsumptionTransaction[]>([]);
  const [loading, setLoading] = useState(true);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const [borrowingsData, consumptionsData] = await Promise.all([
          fetchBorrowings(activeTab === 'borrowing' ? 'ACTIVE' : undefined),
          fetchConsumptions()
        ]);

        setBorrowings(borrowingsData);
        setConsumptions(consumptionsData);
      } catch (error) {
        console.error('Error loading activities data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [activeTab]);

  const toggleRowExpansion = (id: string) => {
    setExpandedRows(prev =>
      prev.includes(id)
        ? prev.filter(rowId => rowId !== id)
        : [...prev, id]
    );
  };

  const isRowExpanded = (id: string) => expandedRows.includes(id);

  const getStatusIcon = (status: string, isOverdue?: boolean) => {
    if (isOverdue) return <AlertTriangle className="w-4 h-4 text-red-500" />;

    switch (status.toLowerCase()) {
      case 'active':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'overdue':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string, isOverdue?: boolean) => {
    if (isOverdue) return "bg-red-100 text-red-800 border-red-200";

    switch (status.toLowerCase()) {
      case 'active':
        return "bg-blue-100 text-blue-800 border-blue-200";
      case 'completed':
        return "bg-green-100 text-green-800 border-green-200";
      case 'overdue':
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleExtendDueDate = (borrowing: BorrowingTransaction) => {
    setSelectedTransaction(borrowing);
    setSidebarType('extend');
    setSidebarOpen(true);
  };

  const handleReturnItems = (borrowing: BorrowingTransaction) => {
    setSelectedTransaction(borrowing);
    setSidebarType('return');
    setSidebarOpen(true);
  };

  const handleCreateNew = () => {
    setSelectedTransaction(null);
    setSidebarType('borrow');
    setSidebarOpen(true);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Activities</h1>
            <p className="text-muted-foreground mt-1">Loading activities data...</p>
          </div>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Activities</h1>
          <p className="text-muted-foreground mt-1">
            Manage borrowing and consumption activities
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link href="/reports">
              <FileText className="w-4 h-4 mr-2" />
              Generate Report
            </Link>
          </Button>
          <Button onClick={handleCreateNew} className="hover-lift">
            <Plus className="w-4 h-4 mr-2" />
            New Activity
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search activities..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
        <Button variant="outline" size="sm">
          <Calendar className="w-4 h-4 mr-2" />
          Date Range
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="borrowing" className="flex items-center space-x-2">
            <ArrowUpRight className="w-4 h-4" />
            <span>Borrowing</span>
            <Badge variant="secondary" className="ml-1">
              {borrowings.filter(b => b.status === 'ACTIVE' || b.status === 'OVERDUE').length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="consuming" className="flex items-center space-x-2">
            <Package className="w-4 h-4" />
            <span>Consuming</span>
            <Badge variant="secondary" className="ml-1">{consumptions.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center space-x-2">
            <Activity className="w-4 h-4" />
            <span>History</span>
          </TabsTrigger>
        </TabsList>

        {/* Borrowing Tab */}
        <TabsContent value="borrowing" className="space-y-4">
          <div className="glass rounded-lg border">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Active Borrowings</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Borrower</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Items</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Due Date</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Actions</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {borrowings.filter(borrowing =>
                      borrowing.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      borrowing.purpose.toLowerCase().includes(searchQuery.toLowerCase())
                    ).map((borrowing) => (
                      <React.Fragment key={borrowing.id}>
                        <tr
                          className="border-b border-gray-100 hover:bg-white/50 cursor-pointer transition-all-smooth"
                          onClick={() => toggleRowExpansion(borrowing.id)}
                        >
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-3">
                              <User className="w-4 h-4 text-muted-foreground" />
                              <div>
                                <div className="font-medium">{borrowing.user.name}</div>
                                <div className="text-sm text-muted-foreground">{borrowing.user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              {borrowing.borrowingItems.slice(0, 2).map(item => item.tool.name).join(', ')}
                              {borrowing.borrowingItems.length > 2 && ` +${borrowing.borrowingItems.length - 2} more`}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Total: {borrowing.totalItems} items
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              {new Date(borrowing.dueDate).toLocaleDateString()}
                            </div>
                            {borrowing.isOverdue && (
                              <div className="text-xs text-red-500">
                                {borrowing.daysOverdue} days overdue
                              </div>
                            )}
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(borrowing.status, borrowing.isOverdue)}
                              <Badge className={cn("text-xs", getStatusColor(borrowing.status, borrowing.isOverdue))}>
                                {borrowing.isOverdue ? 'Overdue' : borrowing.status}
                              </Badge>
                            </div>
                          </td>
                          <td className="py-4 px-4" onClick={(e) => e.stopPropagation()}>
                            <div className="flex space-x-2">
                              {borrowing.canExtend && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleExtendDueDate(borrowing)}
                                >
                                  <RotateCcw className="w-3 h-3 mr-1" />
                                  Extend
                                </Button>
                              )}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleReturnItems(borrowing)}
                              >
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Return
                              </Button>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            {isRowExpanded(borrowing.id) ? (
                              <ChevronUp className="w-4 h-4 text-muted-foreground" />
                            ) : (
                              <ChevronDown className="w-4 h-4 text-muted-foreground" />
                            )}
                          </td>
                        </tr>

                        {/* Expanded Row */}
                        {isRowExpanded(borrowing.id) && (
                          <tr>
                            <td colSpan={6} className="py-4 px-6 bg-gray-50/50">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Borrowing Details</h4>
                                  <p className="text-sm text-muted-foreground">
                                    Borrow Date: {new Date(borrowing.borrowDate).toLocaleDateString()}<br />
                                    Purpose: {borrowing.purpose}<br />
                                    Department: {borrowing.user.department || 'Not specified'}
                                  </p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Items Details</h4>
                                  <div className="space-y-1">
                                    {borrowing.borrowingItems.map(item => (
                                      <div key={item.id} className="text-sm text-muted-foreground flex justify-between">
                                        <span>{item.tool.name} (x{item.quantity})</span>
                                        <span className="text-xs">
                                          {item.returnDate ? 'Returned' : 'Not returned'}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                    {borrowings.length === 0 && (
                      <tr>
                        <td colSpan={6} className="py-8 text-center text-muted-foreground">
                          No active borrowings found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Consuming Tab */}
        <TabsContent value="consuming" className="space-y-4">
          <div className="glass rounded-lg border">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Recent Consumptions</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Consumer</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Materials</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Date</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Project</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground">Value</th>
                      <th className="text-left py-3 px-4 font-medium text-muted-foreground"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {consumptions.filter(consumption =>
                      consumption.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      consumption.purpose.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      (consumption.projectName && consumption.projectName.toLowerCase().includes(searchQuery.toLowerCase()))
                    ).map((consumption) => (
                      <React.Fragment key={consumption.id}>
                        <tr
                          className="border-b border-gray-100 hover:bg-white/50 cursor-pointer transition-all-smooth"
                          onClick={() => toggleRowExpansion(consumption.id)}
                        >
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-3">
                              <User className="w-4 h-4 text-muted-foreground" />
                              <div>
                                <div className="font-medium">{consumption.user.name}</div>
                                <div className="text-sm text-muted-foreground">{consumption.user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              {consumption.consumptionItems.slice(0, 2).map(item =>
                                `${item.material.name} (${item.quantity}${item.material.unit})`
                              ).join(', ')}
                              {consumption.consumptionItems.length > 2 && ` +${consumption.consumptionItems.length - 2} more`}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Total: {consumption.totalItems} items
                            </div>
                          </td>
                          <td className="py-4 px-4 text-sm">
                            {new Date(consumption.consumptionDate).toLocaleDateString()}
                          </td>
                          <td className="py-4 px-4 text-sm">
                            {consumption.projectName || 'No project'}
                          </td>
                          <td className="py-4 px-4 text-sm">
                            {consumption.totalValue ?
                              `Rp ${Number(consumption.totalValue).toLocaleString()}` :
                              'Not specified'
                            }
                          </td>
                          <td className="py-4 px-4">
                            {isRowExpanded(consumption.id) ? (
                              <ChevronUp className="w-4 h-4 text-muted-foreground" />
                            ) : (
                              <ChevronDown className="w-4 h-4 text-muted-foreground" />
                            )}
                          </td>
                        </tr>

                        {/* Expanded Row */}
                        {isRowExpanded(consumption.id) && (
                          <tr>
                            <td colSpan={6} className="py-4 px-6 bg-gray-50/50">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Consumption Details</h4>
                                  <p className="text-sm text-muted-foreground">
                                    Purpose: {consumption.purpose}<br />
                                    Project: {consumption.projectName || 'Not specified'}<br />
                                    Department: {consumption.user.department || 'Not specified'}
                                  </p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Materials Details</h4>
                                  <div className="space-y-1">
                                    {consumption.consumptionItems.map(item => (
                                      <div key={item.id} className="text-sm text-muted-foreground flex justify-between">
                                        <span>{item.material.name} ({item.quantity}{item.material.unit})</span>
                                        <span className="text-xs">
                                          {item.totalValue ? `Rp ${Number(item.totalValue).toLocaleString()}` : 'No price'}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                    {consumptions.length === 0 && (
                      <tr>
                        <td colSpan={6} className="py-8 text-center text-muted-foreground">
                          No recent consumptions found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <div className="glass rounded-lg border">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Transaction History</h3>
              <div className="space-y-3">
                {/* Combined history from borrowings and consumptions */}
                {[...borrowings.filter(b => b.status === 'COMPLETED'), ...consumptions]
                  .sort((a, b) => {
                    const dateA = 'borrowingItems' in a ? a.createdAt : a.consumptionDate;
                    const dateB = 'borrowingItems' in b ? b.createdAt : b.consumptionDate;
                    return new Date(dateB).getTime() - new Date(dateA).getTime();
                  })
                  .slice(0, 10)
                  .map((transaction, index) => {
                    const isBorrowing = 'borrowingItems' in transaction;
                    return (
                      <div key={`${transaction.id}-${index}`} className="p-4 rounded-lg border hover:shadow-md transition-all-smooth glass">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              {isBorrowing ? (
                                <ArrowUpRight className="w-4 h-4 text-blue-500" />
                              ) : (
                                <Package className="w-4 h-4 text-green-500" />
                              )}
                              <h4 className="font-medium text-sm">
                                {isBorrowing ? 'Tool Return' : 'Material Consumption'}
                              </h4>
                              <Badge variant="outline" className="text-xs">
                                {isBorrowing ? 'Completed' : 'Consumed'}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {transaction.user.name} - {transaction.purpose}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {isBorrowing
                                ? new Date(transaction.returnDate!).toLocaleDateString()
                                : new Date(transaction.consumptionDate).toLocaleDateString()
                              }
                            </p>
                          </div>
                          <Button variant="outline" size="sm">
                            <Eye className="w-3 h-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                {borrowings.length === 0 && consumptions.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No transaction history found
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Contextual Sidebar */}
      <ContextualSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        type={sidebarType}
        borrowing={selectedTransaction ? {
          id: selectedTransaction.id,
          borrower: selectedTransaction.user?.name || '',
          items: selectedTransaction.borrowingItems?.map((item: any) => ({
            id: item.id,
            name: item.tool.name,
            quantity: item.quantity,
            originalCondition: item.originalCondition
          })) || [],
          dueDate: selectedTransaction.dueDate || '',
          purpose: selectedTransaction.purpose || ''
        } : undefined}
        onSubmit={(formData: any) => {
          console.log('Form submitted:', formData);
          setSidebarOpen(false);
          // TODO: Implement actual API calls here
        }}
      />
    </div>
  );
}
